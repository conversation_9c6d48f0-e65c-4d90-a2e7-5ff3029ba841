import type { GetDocumentsQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { InfoIcon } from 'lucide-react'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import UpdateBaptismaDialog from './update-baptisma-dialog'
import useDeleteDocument from './use-delete-document'
import useGetDocuments from './use-get-documents'

type BaptismaRecordFromQuery = NonNullable<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]['extra_record']> & { __typename: 'BaptismaRecord' }

export default function BaptismaFilters() {
  const { getBaptisma, handlePage, page, searchBaptisma } = useGetDocuments()
  const [selectedRecord, setSelectedRecord] = useState<BaptismaRecordFromQuery>()
  const [selectedDocument, setSelectedDocument] = useState<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]>()
  const [selectedId, setSelectedId] = useState('')
  const { deleteDocument } = useDeleteDocument()

  const { isOpen, toggle } = useBoolean()
  const { isOpen: openDelete, toggle: toggleDelete } = useBoolean()

  const handleDelete = () => {
    if (!selectedId) {
      return
    }
    deleteDocument.mutate(selectedId, {
      onSuccess: () => {
        setSelectedId('')
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      hming: '',
      pa_hming: '',
      nu_hming: '',
      khua: '',
      pian_ni: '',
      baptisma_chan_ni: '',
      chantirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchBaptisma(value)
    },
  })

  const data = getBaptisma.data?.getDocuments?.data || []
  const lastPage = getBaptisma.data?.getDocuments?.paginator_info?.last_page ?? 1

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="registration_no"
              children={field => <field.InputField label="Registration No" />}
            />
            <form.AppField
              name="hming"
              children={field => <field.InputField label="Mipa hming" />}
            />
            <form.AppField
              name="pa_hming"
              children={field => <field.InputField label="Pa hming" />}
            />
            <form.AppField
              name="nu_hming"
              children={field => <field.InputField label="Nu hming" />}
            />

            <form.AppField
              name="khua"
              children={field => <field.InputField label="Khua" />}
            />
            <form.AppField
              name="pian_ni"
              children={field => <field.InputField label="Pian ni" type="date" />}
            />
            <form.AppField
              name="baptisma_chan_ni"
              children={field => <field.InputField label="Baptisma chan ni" type="date" />}
            />
            <form.AppField
              name="chantirtu"
              children={field => <field.InputField label="Chantirtu" />}
            />
            <div className="col-span-1">
              <Button type="submit" isLoading={getBaptisma.isLoading}>
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <div className="flex grow rounded-md bg-muted p-4">
          <Table className="w-full min-w-[1000px] table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Reg no</TableHead>
                <TableHead className="w-32">Baptisma changtu hming</TableHead>
                <TableHead className="w-32">Baptisma chantirtu</TableHead>
                <TableHead className="w-32">Pian ni</TableHead>
                <TableHead className="w-32">Baptisma Chan ni</TableHead>
                <TableHead className="w-32">Attached files</TableHead>
                <TableHead className="w-32">Added on</TableHead>
                <TableHead className="w-32">Classified</TableHead>
                <TableHead className="w-32 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map(item => (
                item && item.extra_record?.__typename === 'BaptismaRecord' && (
                  <TableRow key={item.id}>
                    <TableCell>{item.extra_record?.baptisma_registration_no || '-'}</TableCell>
                    <TableCell>{item.extra_record?.hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.chantirtu || '-'}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {item.extra_record?.pian_ni ? format(new Date(item.extra_record?.pian_ni), 'yyyy-MM-dd') : '-'}
                        {item.extra_record?.pian_ni_remarks && (
                          <AppTooltip message={item.extra_record?.pian_ni_remarks}>
                            <InfoIcon className="size-4" />
                          </AppTooltip>
                        )}

                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {item.extra_record?.baptisma_chan_ni ? format(new Date(item.extra_record?.baptisma_chan_ni), 'yyyy-MM-dd') : '-'}
                        {item.extra_record?.baptisma_chan_ni_remarks && (
                          <AppTooltip message={item.extra_record?.baptisma_chan_ni_remarks}>
                            <InfoIcon className="size-4" />
                          </AppTooltip>
                        )}

                      </div>
                    </TableCell>
                    <TableCell>{item.files?.length || '0'}</TableCell>
                    <TableCell>{item.added_date ? format(new Date(item.added_date), 'yyyy-MM-dd') : '-' }</TableCell>
                    <TableCell>{item.is_classified ? 'Yes' : 'No'}</TableCell>
                    <TableCell>
                      <div className="flex justify-end gap-x-2">
                        <AppTooltip message="Update">
                          <Button
                            onClick={() => {
                              if (item.extra_record && item.extra_record.__typename === 'BaptismaRecord') {
                                setSelectedRecord(item.extra_record)
                                setSelectedDocument(item)
                              }
                              toggle(true)
                            }}
                            size="icon"
                            variant="success"
                          >
                            <UpdateIcon />
                          </Button>
                        </AppTooltip>
                        <AppTooltip message="Delete document">
                          <Button
                            onClick={() => {
                              if (item.id) {
                                setSelectedId(item.id)
                                toggleDelete(true)
                              }
                            }}
                            size="icon"
                            variant="destructive"
                          >
                            <DeleteIcon />
                          </Button>
                        </AppTooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                )))}
            </TableBody>
          </Table>
        </div>
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
      {selectedRecord && selectedDocument && (
        <UpdateBaptismaDialog
          isOpen={isOpen}
          toggle={(open) => {
            toggle(open)
            setSelectedRecord(undefined)
            setSelectedDocument(undefined)
          }}
          record={selectedRecord}
          _document={selectedDocument}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteDocument.isPending}
          handleConfirm={handleDelete}
          open={openDelete}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
